# Dependencies
/node_modules
/.pnp
.pnp.js

# Testing
/coverage
/cypress/videos
/cypress/screenshots

# Production
/build
/dist

# Cache files
**/node_modules/.cache
**/.cache
**/*.pack
**/*.pack.old
.eslintcache

# Misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.development
.env.production
.env.test

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
logs
*.log

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.sublime-workspace
.project
.classpath
.settings/

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Debug
.chrome/
debug.log

# Package files
package-lock.json
yarn.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# public

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*