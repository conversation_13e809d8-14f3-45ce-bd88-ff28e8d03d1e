{"name": "soulspace-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fontsource/roboto": "^5.1.1", "@mui/icons-material": "^5.14.3", "@mui/lab": "^5.0.0-alpha.140", "@mui/material": "^5.14.5", "@mui/x-data-grid": "^6.11.2", "@mui/x-date-pickers": "^7.27.0", "@reduxjs/toolkit": "^1.9.5", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.4.0", "chart.js": "^4.3.3", "date-fns": "^2.30.0", "emoji-picker-react": "^4.12.2", "formik": "^2.4.3", "framer-motion": "^12.4.7", "jwt-decode": "^3.1.2", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-quill": "^2.0.0", "react-redux": "^8.1.2", "react-router-dom": "^6.15.0", "react-scripts": "5.0.1", "react-toastify": "^9.1.3", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "web-vitals": "^2.1.4", "yup": "^1.2.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}