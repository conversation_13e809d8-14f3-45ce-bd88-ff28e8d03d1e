const errorHandler = (err, req, res, next) => {
    console.error(err.stack);
    if (err.name === 'ValidationError') {
        return res.status(400).json({ message: err.message });
    }
    if (err.name === 'MongoError' && err.code === 11000) {
        return res.status(409).json({ message: 'Duplicate key error' });
    }
    res.status(500).json({ message: 'Internal Server Error' });
};

module.exports = errorHandler; 