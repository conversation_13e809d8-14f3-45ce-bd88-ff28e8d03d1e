import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from '../../utils/axiosConfig';

export const fetchPatients = createAsyncThunk(
  'patients/fetchPatients',
  async (_, { getState }) => {
    // Get token from state
    const state = getState();
    const newToken = state.userAuth?.token;
    const oldToken = state.auth?.token;
    const token = newToken || oldToken;

    console.log('patientSlice: Fetching patients with token:', !!token);

    const config = {
      headers: token ? { Authorization: `Bearer ${token}` } : {}
    };

    try {
      const response = await axios.get('/api/patients', config);
      return response.data;
    } catch (error) {
      console.log('API endpoint for patients not available, using mock data');
      // Mock data for patients
      const mockPatients = [
        {
          _id: 'p1',
          name: '<PERSON>',
          email: '<EMAIL>',
          phone: '+****************',
          age: 45,
          gender: 'Male',
          address: '123 Main St, Anytown, USA',
          medicalHistory: 'Hypertension, Diabetes',
          insuranceProvider: 'Blue Cross',
          insuranceNumber: 'BC12345678',
          emergencyContact: '<PERSON>',
          emergencyPhone: '+****************',
          createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          _id: 'p2',
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          phone: '+****************',
          age: 32,
          gender: 'Female',
          address: '456 Oak Ave, Somewhere, USA',
          medicalHistory: 'Asthma',
          insuranceProvider: 'Aetna',
          insuranceNumber: 'AE87654321',
          emergencyContact: 'Michael Johnson',
          emergencyPhone: '+****************',
          createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          _id: 'p3',
          name: 'Robert Wilson',
          email: '<EMAIL>',
          phone: '+****************',
          age: 67,
          gender: 'Male',
          address: '789 Pine St, Elsewhere, USA',
          medicalHistory: 'Heart Disease, Arthritis',
          insuranceProvider: 'Medicare',
          insuranceNumber: 'MC98765432',
          emergencyContact: 'Mary Wilson',
          emergencyPhone: '+****************',
          createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          _id: 'p4',
          name: 'Emily Davis',
          email: '<EMAIL>',
          phone: '+****************',
          age: 28,
          gender: 'Female',
          address: '101 Maple Dr, Nowhere, USA',
          medicalHistory: 'Allergies',
          insuranceProvider: 'Cigna',
          insuranceNumber: 'CI24681357',
          emergencyContact: 'David Davis',
          emergencyPhone: '+****************',
          createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          _id: 'p5',
          name: 'Michael Brown',
          email: '<EMAIL>',
          phone: '+****************',
          age: 52,
          gender: 'Male',
          address: '202 Cedar Ln, Anyplace, USA',
          medicalHistory: 'High Cholesterol',
          insuranceProvider: 'United Healthcare',
          insuranceNumber: 'UH13579246',
          emergencyContact: 'Susan Brown',
          emergencyPhone: '+****************',
          createdAt: new Date(Date.now() - 75 * 24 * 60 * 60 * 1000).toISOString()
        }
      ];
      return mockPatients;
    }
  }
);

export const createPatient = createAsyncThunk(
  'patients/createPatient',
  async (patientData, { getState }) => {
    // Get token from state
    const state = getState();
    const newToken = state.userAuth?.token;
    const oldToken = state.auth?.token;
    const token = newToken || oldToken;

    console.log('patientSlice: Creating patient with token:', !!token);

    const config = {
      headers: token ? { Authorization: `Bearer ${token}` } : {}
    };

    try {
      const response = await axios.post('/api/patients', patientData, config);
      return response.data;
    } catch (error) {
      console.log('API endpoint for creating patient not available, using mock data');
      // Create a mock patient with the provided data
      return {
        _id: 'p' + Date.now(), // Generate a unique ID
        ...patientData,
        createdAt: new Date().toISOString()
      };
    }
  }
);

export const updatePatient = createAsyncThunk(
  'patients/updatePatient',
  async ({ patientId, patientData }, { getState }) => {
    // Get token from state
    const state = getState();
    const newToken = state.userAuth?.token;
    const oldToken = state.auth?.token;
    const token = newToken || oldToken;

    console.log('patientSlice: Updating patient with token:', !!token);

    const config = {
      headers: token ? { Authorization: `Bearer ${token}` } : {}
    };

    try {
      const response = await axios.put(`/api/patients/${patientId}`, patientData, config);
      return response.data;
    } catch (error) {
      console.log('API endpoint for updating patient not available, using mock data');
      // Get the current state to find the patient to update
      const currentState = getState();
      const existingPatient = currentState.patient.patients.find(p => p._id === patientId);

      if (!existingPatient) {
        throw new Error('Patient not found');
      }

      // Return updated patient
      return {
        ...existingPatient,
        ...patientData,
        _id: patientId, // Ensure ID is preserved
        updatedAt: new Date().toISOString()
      };
    }
  }
);

export const deletePatient = createAsyncThunk(
  'patients/deletePatient',
  async (patientId, { getState }) => {
    // Get token from state
    const state = getState();
    const newToken = state.userAuth?.token;
    const oldToken = state.auth?.token;
    const token = newToken || oldToken;

    console.log('patientSlice: Deleting patient with token:', !!token);

    const config = {
      headers: token ? { Authorization: `Bearer ${token}` } : {}
    };

    try {
      await axios.delete(`/api/patients/${patientId}`, config);
      return patientId;
    } catch (error) {
      console.log('API endpoint for deleting patient not available, using mock data');
      // For delete operations, we just need to return the ID to remove it from the state
      return patientId;
    }
  }
);

const patientSlice = createSlice({
  name: 'patients',
  initialState: {
    patients: [],
    selectedPatient: null,
    loading: false,
    error: null,
  },
  reducers: {
    setSelectedPatient: (state, action) => {
      state.selectedPatient = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPatients.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPatients.fulfilled, (state, action) => {
        state.loading = false;
        state.patients = action.payload;
      })
      .addCase(fetchPatients.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(createPatient.fulfilled, (state, action) => {
        state.patients.push(action.payload);
      })
      .addCase(updatePatient.fulfilled, (state, action) => {
        const index = state.patients.findIndex(
          (patient) => patient._id === action.payload._id
        );
        if (index !== -1) {
          state.patients[index] = action.payload;
        }
        if (state.selectedPatient?._id === action.payload._id) {
          state.selectedPatient = action.payload;
        }
      })
      .addCase(deletePatient.fulfilled, (state, action) => {
        state.patients = state.patients.filter(
          (patient) => patient._id !== action.payload
        );
        if (state.selectedPatient?._id === action.payload) {
          state.selectedPatient = null;
        }
      });
  },
});

export const { setSelectedPatient } = patientSlice.actions;
export default patientSlice.reducer;
