import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  TextField,
  Button,
  Divider,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Grid,
  IconButton,
  useTheme,
  alpha,
  <PERSON>readcrumbs,
  Link,
  Alert,
} from '@mui/material';
import {
  Forum,
  Home,
  HelpCenter,
  ArrowBack,
  Add,
  Close,
  Image,
  AttachFile,
  FormatBold,
  FormatItalic,
  FormatListBulleted,
  FormatListNumbered,
  FormatQuote,
  Link as LinkIcon,
  Save,
  Send,
  Preview,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { Link as RouterLink } from 'react-router-dom';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { usePosts } from '../../context/PostContext';

const categories = [
  { id: 1, name: 'General Discussion' },
  { id: 2, name: 'Health Tips' },
  { id: 3, name: 'Success Stories' },
  { id: 4, name: 'Questions & Support' },
  { id: 5, name: 'Wellness Challenges' },
  { id: 6, name: 'Nutrition & Diet' },
  { id: 7, name: 'Fitness & Exercise' },
  { id: 8, name: 'Mental Health' },
  { id: 9, name: 'Sleep Improvement' },
  { id: 10, name: 'Chronic Condition Management' },
];

const CreatePost = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { addPost } = usePosts();

  // Form state
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [category, setCategory] = useState('');
  const [tags, setTags] = useState([]);
  const [currentTag, setCurrentTag] = useState('');
  const [errors, setErrors] = useState({});
  const [error, setError] = useState(''); // Add missing error state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        when: "beforeChildren",
        staggerChildren: 0.1,
        duration: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { duration: 0.5 } }
  };

  // Rich text editor modules
  const modules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered' }, { 'list': 'bullet' }],
      ['blockquote', 'link', 'image'],
      ['clean']
    ],
  };

  const formats = [
    'header',
    'bold', 'italic', 'underline', 'strike',
    'list', 'bullet',
    'blockquote', 'link', 'image'
  ];

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const newPost = {
        title: title.trim(),
        content: content.trim(),
        category: category,
        tags: tags.filter(tag => tag.trim() !== ''), // Filter empty tags
        // Remove the ID as it will be generated by MongoDB
        // Remove author details as they will come from the token
        timestamp: new Date().toISOString(),
      };

      const createdPost = await addPost(newPost);
      setIsSubmitting(false);
      setShowSuccess(true);

      setTimeout(() => {
        navigate('/community');
      }, 1500);
    } catch (error) {
      console.error('Error creating post:', error);
      setIsSubmitting(false);
      setError('Failed to create post. Please try again.');
    }
  };

  // Handle tag addition
  const handleAddTag = () => {
    if (currentTag.trim() && !tags.includes(currentTag.trim().toLowerCase()) && tags.length < 5) {
      setTags([...tags, currentTag.trim().toLowerCase()]);
      setCurrentTag('');
    }
  };

  // Handle tag removal
  const handleRemoveTag = (tagToRemove) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  // Handle tag input keypress
  const handleTagKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box component={motion.div} variants={containerVariants} initial="hidden" animate="visible">
        {/* Breadcrumbs */}
        <Breadcrumbs sx={{ mb: 2 }}>
          <Link component={RouterLink} to="/" color="inherit" sx={{ display: 'flex', alignItems: 'center' }}>
            <Home fontSize="small" sx={{ mr: 0.5 }} />
            Home
          </Link>
          <Link component={RouterLink} to="/help" color="inherit" sx={{ display: 'flex', alignItems: 'center' }}>
            <HelpCenter fontSize="small" sx={{ mr: 0.5 }} />
            Help Center
          </Link>
          <Link component={RouterLink} to="/community" color="inherit" sx={{ display: 'flex', alignItems: 'center' }}>
            <Forum fontSize="small" sx={{ mr: 0.5 }} />
            Community Forum
          </Link>
          <Typography color="text.primary">
            Create Post
          </Typography>
        </Breadcrumbs>

        <Button
          component={RouterLink}
          to="/community"
          startIcon={<ArrowBack />}
          sx={{ mb: 3 }}
        >
          Back to Community
        </Button>

        {/* Success Alert */}
        {showSuccess && (
          <Alert
            severity="success"
            sx={{ mb: 3 }}
            onClose={() => setShowSuccess(false)}
          >
            Your post has been created successfully! Redirecting...
          </Alert>
        )}

        {/* Error Alert */}
        {error && (
          <Alert
            severity="error"
            sx={{ mb: 3 }}
            onClose={() => setError('')}
          >
            {error}
          </Alert>
        )}

        {/* Create Post Form */}
        <Paper
          component={motion.div}
          variants={itemVariants}
          sx={{
            p: 4,
            borderRadius: '16px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          }}
        >
          <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
            Create a New Post
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Share your thoughts, questions, or experiences with the community. Please follow our community guidelines when posting.
          </Typography>

          <Divider sx={{ my: 3 }} />

          <Box component="form" onSubmit={handleSubmit} noValidate>
            <Grid container spacing={3}>
              {/* Title */}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Post Title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  error={!!errors.title}
                  helperText={errors.title || 'Create a clear, concise title for your post (max 100 characters)'}
                  inputProps={{ maxLength: 100 }}
                  required
                />
              </Grid>

              {/* Category */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth error={!!errors.category} required>
                  <InputLabel>Category</InputLabel>
                  <Select
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                    label="Category"
                  >
                    {categories.map((cat) => (
                      <MenuItem key={cat.id} value={cat.name}>
                        {cat.name}
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText>
                    {errors.category || 'Select the most relevant category for your post'}
                  </FormHelperText>
                </FormControl>
              </Grid>

              {/* Tags */}
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Tags"
                  value={currentTag}
                  onChange={(e) => setCurrentTag(e.target.value)}
                  helperText={`Add up to 5 tags (press Enter after each tag) - ${5 - tags.length} remaining`}
                  onKeyPress={handleTagKeyPress}
                  InputProps={{
                    endAdornment: (
                      <IconButton
                        onClick={handleAddTag}
                        disabled={!currentTag.trim() || tags.length >= 5}
                        size="small"
                      >
                        <Add />
                      </IconButton>
                    ),
                  }}
                />
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                  {tags.map((tag) => (
                    <Chip
                      key={tag}
                      label={tag}
                      onDelete={() => handleRemoveTag(tag)}
                      size="small"
                      sx={{
                        borderRadius: '4px',
                        height: 24,
                        fontSize: '0.75rem',
                      }}
                    />
                  ))}
                </Box>
              </Grid>

              {/* Content */}
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  Post Content
                </Typography>
                <Box
                  sx={{
                    '.ql-container': {
                      borderBottomLeftRadius: '8px',
                      borderBottomRightRadius: '8px',
                      minHeight: '200px',
                    },
                    '.ql-toolbar': {
                      borderTopLeftRadius: '8px',
                      borderTopRightRadius: '8px',
                    },
                    ...(errors.content && {
                      '.ql-container, .ql-toolbar': {
                        borderColor: theme.palette.error.main,
                      },
                    }),
                  }}
                >
                  <ReactQuill
                    theme="snow"
                    value={content}
                    onChange={setContent}
                    modules={modules}
                    formats={formats}
                    placeholder="Write your post here..."
                  />
                </Box>
                {errors.content && (
                  <FormHelperText error>
                    {errors.content}
                  </FormHelperText>
                )}
                <FormHelperText>
                  Use the formatting tools to make your post more readable. You can add images, links, and more.
                </FormHelperText>
              </Grid>

              {/* Community Guidelines Reminder */}
              <Grid item xs={12}>
                <Paper
                  sx={{
                    p: 2,
                    bgcolor: alpha(theme.palette.info.main, 0.1),
                    border: `1px solid ${alpha(theme.palette.info.main, 0.3)}`,
                    borderRadius: '8px',
                  }}
                >
                  <Typography variant="subtitle2" color="info.main" gutterBottom>
                    Community Guidelines Reminder
                  </Typography>
                  <Typography variant="body2">
                    Please ensure your post follows our <Link component={RouterLink} to="/community/guidelines">community guidelines</Link>. Be respectful, supportive, and avoid sharing personal medical information. Posts that violate our guidelines may be removed.
                  </Typography>
                </Paper>
              </Grid>

              {/* Submit Buttons */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
                  <Button
                    variant="outlined"
                    onClick={() => navigate('/community')}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<Save />}
                    disabled={isSubmitting}
                  >
                    Save Draft
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    startIcon={<Send />}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'Posting...' : 'Post to Community'}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default CreatePost;




